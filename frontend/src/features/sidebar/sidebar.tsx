import clsx from 'clsx'
import { MessageSquareMore, PanelRightOpen } from 'lucide-react'

import { useSidebarStore } from '@/services/sidebar'

import { SidebarContent } from './ui/kit/sidebar-content'
import { SidebarFooter } from './ui/kit/sidebar-footer'
import { SidebarHeader } from './ui/kit/sidebar-header'
import { MainNav } from './ui/main-nav'
import logo from './assets/Logo.svg'
import logoText from './assets/logo-text.svg'
import { Link } from '@tanstack/react-router'
import { SidebarMenuButton } from './ui/kit/sidebar-menu'

export const ICON_SIZE = '1.25rem'

export function Sidebar() {
    const { isOpen, toggleOpen } = useSidebarStore()

    return (
        <aside
            className={clsx(
                `group shrink-0 h-screen sticky top-0 text-sidebar-primary-foreground ease-linear overflow-y-auto overflow-x-hidden bg-sidebar transition-all duration-100`,
                isOpen ? 'w-[var(--sidebar-width-expanded)]' : 'w-[var(--sidebar-width-collapsed)]',
            )}
            data-state={isOpen ? 'open' : 'collapsed'}
        >
            <div className="flex flex-col h-full w-full overflow-hidden">
                <SidebarHeader className="h-[100px]">
                    <Link
                        to="/"
                        className="flex flex-row gap-3"
                    >
                        <img
                            src={logo}
                            alt="Логотип Simbios"
                        />
                        {isOpen && <img src={logoText} />}
                    </Link>
                    <button
                        className="ml-auto"
                        type="button"
                        onClick={toggleOpen}
                    >
                        <PanelRightOpen size={ICON_SIZE} />
                    </button>
                </SidebarHeader>
                <SidebarContent>
                    <MainNav />
                </SidebarContent>
                <SidebarFooter className="pt-0">
                    <SidebarMenuButton
                        variant={'footer'}
                        size={'lg'}
                    >
                        <MessageSquareMore size={ICON_SIZE} />
                        <span>Мессенджер</span>
                    </SidebarMenuButton>
                </SidebarFooter>
            </div>
        </aside>
    )
}
