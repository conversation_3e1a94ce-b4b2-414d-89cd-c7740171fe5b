import { useState } from 'react'
import { Box, Folder, GitFork, Gpu, Home } from 'lucide-react'

import { SidebarGroup, SidebarGroupLabel } from './kit/sidebar-group'
import { SidebarMenu, SidebarMenuLink } from './kit/sidebar-menu'
import { ICON_SIZE } from '../sidebar'
import { Separator } from './kit/sidebar-separator'
import { SidebarCollapsibleGroup } from './kit/sidebar-collapsible-group'
import { SidebarSearch, useMenuFilter } from './kit/sidebar-search'

// Типы для элементов меню
interface MenuItem {
    href: string
    label: string
    icon: React.ComponentType<{ size: string }>
}

interface MenuGroup {
    title: string
    items: MenuItem[]
    defaultOpen?: boolean
}

// Данные меню
const menuData: MenuGroup[] = [
    {
        title: 'Основное',
        defaultOpen: true,
        items: [
            { href: '/', label: 'Рабо<PERSON>ий стол', icon: Home },
            { href: '/test', label: 'Календарь', icon: Folder },
        ],
    },
    {
        title: 'Планирование',
        defaultOpen: true,
        items: [
            { href: '#', label: 'Календарный план', icon: GitFork },
            { href: '/objects', label: 'Сетевой график', icon: Box },
            { href: '/processes', label: 'Бюджет', icon: Gpu },
        ],
    },
    {
        title: 'Ресурсы',
        defaultOpen: false,
        items: [
            { href: '#', label: 'Команда', icon: GitFork },
            { href: '/objects', label: 'Структура команды', icon: Box },
            { href: '/processes', label: 'Матрица ответственности', icon: Gpu },
        ],
    },
    {
        title: 'Аналитика',
        defaultOpen: false,
        items: [
            { href: '#', label: 'Отчёты и метрики', icon: GitFork },
            { href: '/objects', label: 'Паспорт проекта', icon: Box },
        ],
    },
    {
        title: 'Настройки',
        defaultOpen: false,
        items: [
            { href: '#', label: 'Настройки', icon: GitFork },
        ],
    },
]

export function MainNav() {
    const [searchQuery, setSearchQuery] = useState('')

    // Фильтруем все элементы меню по поисковому запросу
    const allItems = menuData.flatMap((group) =>
        group.items.map((item) => ({ ...item, groupTitle: group.title })),
    )

    const filteredItems = useMenuFilter(
        allItems,
        searchQuery,
        (item) => `${item.label} ${item.groupTitle}`,
    )

    // Если есть поисковый запрос, показываем плоский список результатов
    if (searchQuery.trim()) {
        return (
            <div className="space-y-2">
                <SidebarSearch
                    onSearch={setSearchQuery}
                    className="px-2"
                />
                <SidebarGroup>
                    <SidebarGroupLabel>Результаты поиска</SidebarGroupLabel>
                    <SidebarMenu>
                        {filteredItems.map((item, index) => {
                            const Icon = item.icon
                            return (
                                <SidebarMenuLink
                                    key={`${item.href}-${index}`}
                                    href={item.href}
                                >
                                    <Icon size={ICON_SIZE} />
                                    <span>{item.label}</span>
                                </SidebarMenuLink>
                            )
                        })}
                    </SidebarMenu>
                    {filteredItems.length === 0 && (
                        <div className="px-2 py-4 text-sm text-sidebar-foreground/60">
                            Ничего не найдено
                        </div>
                    )}
                </SidebarGroup>
            </div>
        )
    }

    // Обычное отображение с группами
    return (
        <div className="space-y-2">
            <SidebarSearch
                onSearch={setSearchQuery}
                className="px-2"
            />

            {menuData.map((group, groupIndex) => (
                <div key={group.title}>
                    <SidebarCollapsibleGroup
                        title={group.title}
                        defaultOpen={group.defaultOpen}
                    >
                        <SidebarMenu>
                            {group.items.map((item, itemIndex) => {
                                const Icon = item.icon
                                return (
                                    <SidebarMenuLink
                                        key={`${item.href}-${itemIndex}`}
                                        href={item.href}
                                    >
                                        <Icon size={ICON_SIZE} />
                                        <span>{item.label}</span>
                                    </SidebarMenuLink>
                                )
                            })}
                        </SidebarMenu>
                    </SidebarCollapsibleGroup>
                    {groupIndex < menuData.length - 1 && <Separator />}
                </div>
            ))}
        </div>
    )
}
