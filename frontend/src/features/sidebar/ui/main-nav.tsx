import { Box, Folder, GitFork, Gpu, Home } from 'lucide-react'

import { SidebarGroup, SidebarGroupLabel } from './kit/sidebar-group'
import { SidebarMenu, SidebarMenuLink } from './kit/sidebar-menu'
import { ICON_SIZE } from '../sidebar'
import { Separator } from './kit/sidebar-separator'

export function MainNav() {
    return (
        <div className="overflow-x-hidden">
            <SidebarGroup>
                <SidebarGroupLabel>Основное</SidebarGroupLabel>
                <SidebarMenu>
                    <SidebarMenuLink href="/">
                        <Home size={ICON_SIZE} />
                        <span>Рабочий стол</span>
                    </SidebarMenuLink>
                    <SidebarMenuLink href="/test">
                        <Folder size={ICON_SIZE} />
                        <span>Календарь</span>
                    </SidebarMenuLink>
                </SidebarMenu>
            </SidebarGroup>
            <Separator />
            <SidebarGroup>
                <SidebarGroupLabel>Планирование</SidebarGroupLabel>
                <SidebarMenu>
                    <SidebarMenuLink href="#">
                        <GitFork size={ICON_SIZE} />
                        <span>Календарный план</span>
                    </SidebarMenuLink>
                    <SidebarMenuLink href="/objects">
                        <Box size={ICON_SIZE} />
                        <span>Сетевой график</span>
                    </SidebarMenuLink>
                    <SidebarMenuLink href="/processes">
                        <Gpu size={ICON_SIZE} />
                        <span>Бюджет</span>
                    </SidebarMenuLink>
                </SidebarMenu>
            </SidebarGroup>
            <Separator />
            <SidebarGroup>
                <SidebarGroupLabel>Ресурсы</SidebarGroupLabel>
                <SidebarMenu>
                    <SidebarMenuLink href="#">
                        <GitFork size={ICON_SIZE} />
                        <span>Команда</span>
                    </SidebarMenuLink>
                    <SidebarMenuLink href="/objects">
                        <Box size={ICON_SIZE} />
                        <span>Структура команды</span>
                    </SidebarMenuLink>
                    <SidebarMenuLink href="/processes">
                        <Gpu size={ICON_SIZE} />
                        <span>Матрица ответственности</span>
                    </SidebarMenuLink>
                </SidebarMenu>
            </SidebarGroup>
            <Separator />
            <SidebarGroup>
                <SidebarGroupLabel>Аналитика</SidebarGroupLabel>
                <SidebarMenu>
                    <SidebarMenuLink href="#">
                        <GitFork size={ICON_SIZE} />
                        <span>Отчёты и метрики</span>
                    </SidebarMenuLink>
                    <SidebarMenuLink href="/objects">
                        <Box size={ICON_SIZE} />
                        <span>Паспорт проекта</span>
                    </SidebarMenuLink>
                </SidebarMenu>
            </SidebarGroup>
            <Separator />
            <SidebarGroup>
                <SidebarGroupLabel>Настройки</SidebarGroupLabel>
                <SidebarMenu>
                    <SidebarMenuLink href="#">
                        <GitFork size={ICON_SIZE} />
                        <span>Настройки</span>
                    </SidebarMenuLink>
                </SidebarMenu>
            </SidebarGroup>
        </div>
    )
}
