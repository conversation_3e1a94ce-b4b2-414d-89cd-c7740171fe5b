import { useState, useMemo } from 'react'
import { Search, X } from 'lucide-react'
import clsx from 'clsx'

import { ICON_SIZE } from '../../sidebar'

interface SidebarSearchProps {
    placeholder?: string
    onSearch?: (query: string) => void
    className?: string
}

export function SidebarSearch({
    placeholder = 'Поиск...',
    onSearch,
    className,
}: SidebarSearchProps) {
    const [query, setQuery] = useState('')
    const [isFocused, setIsFocused] = useState(false)

    const handleChange = (value: string) => {
        setQuery(value)
        onSearch?.(value)
    }

    const clearSearch = () => {
        setQuery('')
        onSearch?.('')
    }

    return (
        <div className={clsx('relative', className)}>
            <div
                className={clsx(
                    'relative flex items-center rounded-md border transition-colors',
                    'bg-sidebar-accent/5 border-sidebar-border',
                    isFocused && 'ring-2 ring-sidebar-ring border-sidebar-accent'
                )}
            >
                <Search
                    size={ICON_SIZE}
                    className="absolute left-3 text-sidebar-foreground/60"
                />
                <input
                    type="text"
                    value={query}
                    onChange={(e) => handleChange(e.target.value)}
                    onFocus={() => setIsFocused(true)}
                    onBlur={() => setIsFocused(false)}
                    placeholder={placeholder}
                    className={clsx(
                        'w-full bg-transparent pl-10 pr-10 py-2 text-sm',
                        'text-sidebar-foreground placeholder:text-sidebar-foreground/60',
                        'focus:outline-none'
                    )}
                />
                {query && (
                    <button
                        type="button"
                        onClick={clearSearch}
                        className={clsx(
                            'absolute right-3 p-0.5 rounded-sm',
                            'text-sidebar-foreground/60 hover:text-sidebar-foreground',
                            'hover:bg-sidebar-accent/10 transition-colors'
                        )}
                    >
                        <X size={ICON_SIZE} />
                    </button>
                )}
            </div>
        </div>
    )
}

// Хук для фильтрации элементов меню
export function useMenuFilter<T>(
    items: T[],
    searchQuery: string,
    getSearchableText: (item: T) => string
) {
    return useMemo(() => {
        if (!searchQuery.trim()) return items

        const query = searchQuery.toLowerCase()
        return items.filter((item) =>
            getSearchableText(item).toLowerCase().includes(query)
        )
    }, [items, searchQuery, getSearchableText])
}
