import { useState } from 'react'
import { ChevronDown, ChevronRight } from 'lucide-react'
import clsx from 'clsx'

import { SidebarGroup, SidebarGroupLabel } from './sidebar-group'
import { ICON_SIZE } from '../../sidebar'

interface SidebarCollapsibleGroupProps {
    title: string
    children: React.ReactNode
    defaultOpen?: boolean
    className?: string
}

export function SidebarCollapsibleGroup({
    title,
    children,
    defaultOpen = true,
    className,
}: SidebarCollapsibleGroupProps) {
    const [isOpen, setIsOpen] = useState(defaultOpen)

    return (
        <SidebarGroup className={className}>
            <SidebarGroupLabel asChild>
                <button
                    type="button"
                    onClick={() => setIsOpen(!isOpen)}
                    className={clsx(
                        'flex w-full items-center justify-between',
                        'hover:bg-sidebar-accent/10 rounded-md px-2 py-1 transition-colors',
                        'focus:outline-none focus:ring-2 focus:ring-sidebar-ring'
                    )}
                >
                    <span>{title}</span>
                    {isOpen ? (
                        <ChevronDown size={ICON_SIZE} className="shrink-0" />
                    ) : (
                        <ChevronRight size={ICON_SIZE} className="shrink-0" />
                    )}
                </button>
            </SidebarGroupLabel>
            <div
                className={clsx(
                    'overflow-hidden transition-all duration-200 ease-in-out',
                    isOpen ? 'max-h-[1000px] opacity-100' : 'max-h-0 opacity-0'
                )}
            >
                {children}
            </div>
        </SidebarGroup>
    )
}
