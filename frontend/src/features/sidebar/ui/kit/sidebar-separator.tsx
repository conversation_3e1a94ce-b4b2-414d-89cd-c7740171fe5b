import { cva } from 'class-variance-authority'

import { cn } from '@/shared/lib/css'
import { Separator as SeparatorPrimitive } from '@/shared/ui/kit/separator'

interface Separator {
    className?: string
    variant?: 'header'
}

const separatorVariants = cva('bg-sidebar-border left-0 right-0', {
    variants: {
        variant: {
            // Для того чтобы сепаратор совпадал с бордером мэйн хедера
            header: '-mt-px',
        },
    },
})

export function Separator({ className, variant, ...props }: Separator) {
    return (
        <SeparatorPrimitive
            className={cn(separatorVariants({ variant, className }))}
            {...props}
        />
    )
}
