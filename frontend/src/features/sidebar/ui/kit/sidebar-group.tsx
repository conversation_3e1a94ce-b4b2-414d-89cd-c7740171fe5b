import { useSidebarStore } from '@/services/sidebar'
import clsx from 'clsx'

export function SidebarGroup({ className, children, ...props }: React.ComponentProps<'div'>) {
    return (
        <div
            className={clsx('relative flex w-full min-w-0 flex-col p-2', className)}
            {...props}
        >
            {children}
        </div>
    )
}

export function SidebarGroupLabel({ className, children, ...props }: React.ComponentProps<'div'>) {
    const { isOpen } = useSidebarStore()

    return (
        <div
            className={clsx(
                'flex items-center h-8 px-6 text-xs uppercase duration-100 ease-out text-sidebar-primary-foreground',
                !isOpen && 'opacity-0',
                className,
            )}
            {...props}
        >
            {children}
        </div>
    )
}
