import { useState, useEffect, useRef, useMemo } from 'react'
import clsx from 'clsx'

interface VirtualListItem {
    id: string
    height: number
    content: React.ReactNode
}

interface SidebarVirtualListProps {
    items: VirtualListItem[]
    containerHeight: number
    className?: string
    overscan?: number // Количество дополнительных элементов для рендера
}

export function SidebarVirtualList({
    items,
    containerHeight,
    className,
    overscan = 5,
}: SidebarVirtualListProps) {
    const [scrollTop, setScrollTop] = useState(0)
    const scrollElementRef = useRef<HTMLDivElement>(null)

    // Вычисляем позиции элементов
    const itemPositions = useMemo(() => {
        const positions: { start: number; end: number }[] = []
        let currentPosition = 0

        for (const item of items) {
            positions.push({
                start: currentPosition,
                end: currentPosition + item.height,
            })
            currentPosition += item.height
        }

        return positions
    }, [items])

    const totalHeight = itemPositions[itemPositions.length - 1]?.end || 0

    // Определяем видимые элементы
    const visibleRange = useMemo(() => {
        const start = Math.max(
            0,
            itemPositions.findIndex((pos) => pos.end > scrollTop) - overscan
        )
        const end = Math.min(
            items.length - 1,
            itemPositions.findIndex((pos) => pos.start > scrollTop + containerHeight) + overscan
        )

        return { start, end: end === -1 ? items.length - 1 : end }
    }, [scrollTop, containerHeight, itemPositions, overscan, items.length])

    const visibleItems = useMemo(() => {
        const result = []
        for (let i = visibleRange.start; i <= visibleRange.end; i++) {
            if (items[i]) {
                result.push({
                    ...items[i],
                    index: i,
                    top: itemPositions[i].start,
                })
            }
        }
        return result
    }, [visibleRange, items, itemPositions])

    const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
        setScrollTop(e.currentTarget.scrollTop)
    }

    return (
        <div
            ref={scrollElementRef}
            className={clsx(
                'overflow-auto',
                // Кастомный скролл
                'scrollbar-thin scrollbar-track-transparent scrollbar-thumb-sidebar-accent/20 hover:scrollbar-thumb-sidebar-accent/40',
                'scroll-smooth',
                className
            )}
            style={{ height: containerHeight }}
            onScroll={handleScroll}
        >
            <div style={{ height: totalHeight, position: 'relative' }}>
                {visibleItems.map((item) => (
                    <div
                        key={item.id}
                        style={{
                            position: 'absolute',
                            top: item.top,
                            height: item.height,
                            width: '100%',
                        }}
                    >
                        {item.content}
                    </div>
                ))}
            </div>
        </div>
    )
}

// Хук для создания виртуализированного списка из обычных элементов
export function useVirtualizedMenuItems(
    items: Array<{ id: string; content: React.ReactNode }>,
    itemHeight = 40
): VirtualListItem[] {
    return useMemo(
        () =>
            items.map((item) => ({
                id: item.id,
                height: itemHeight,
                content: item.content,
            })),
        [items, itemHeight]
    )
}
