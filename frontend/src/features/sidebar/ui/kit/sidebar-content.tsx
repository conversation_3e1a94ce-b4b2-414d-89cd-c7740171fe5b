import clsx from 'clsx'

export function SidebarContent({ className, ...props }: React.ComponentProps<'div'>) {
    return (
        <nav
            className={clsx(
                'flex min-h-0 flex-1 flex-col gap-2 overflow-auto',
                // Кастомный скролл
                'scrollbar-thin scrollbar-track-transparent scrollbar-thumb-sidebar-accent/20 hover:scrollbar-thumb-sidebar-accent/40',
                // Плавная прокрутка
                'scroll-smooth',
                className,
            )}
            {...props}
        />
    )
}
