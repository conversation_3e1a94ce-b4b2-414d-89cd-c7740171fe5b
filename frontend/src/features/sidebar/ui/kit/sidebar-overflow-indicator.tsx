import { useState, useRef, useEffect } from 'react'
import { ChevronDown, MoreHorizontal } from 'lucide-react'
import clsx from 'clsx'

import { ICON_SIZE } from '../../sidebar'

interface SidebarOverflowIndicatorProps {
    totalItems: number
    visibleItems: number
    onShowMore?: () => void
    className?: string
}

export function SidebarOverflowIndicator({
    totalItems,
    visibleItems,
    onShowMore,
    className,
}: SidebarOverflowIndicatorProps) {
    const hiddenCount = totalItems - visibleItems

    if (hiddenCount <= 0) return null

    return (
        <button
            type="button"
            onClick={onShowMore}
            className={clsx(
                'flex w-full items-center justify-between px-3 py-2 text-sm',
                'text-sidebar-foreground/60 hover:text-sidebar-foreground',
                'hover:bg-sidebar-accent/10 rounded-md transition-colors',
                'focus:outline-none focus:ring-2 focus:ring-sidebar-ring',
                className
            )}
        >
            <span className="flex items-center gap-2">
                <MoreHorizontal size={ICON_SIZE} />
                Ещё {hiddenCount} элементов
            </span>
            <ChevronDown size={ICON_SIZE} />
        </button>
    )
}

// Хук для управления "показать больше" функциональностью
export function useShowMore(initialCount: number, increment: number = 10) {
    const [visibleCount, setVisibleCount] = useState(initialCount)

    const showMore = () => {
        setVisibleCount(prev => prev + increment)
    }

    const reset = () => {
        setVisibleCount(initialCount)
    }

    return {
        visibleCount,
        showMore,
        reset,
    }
}

// Компонент для отслеживания скролла и автоматической подгрузки
interface InfiniteScrollProps {
    children: React.ReactNode
    onLoadMore: () => void
    hasMore: boolean
    loading?: boolean
    threshold?: number // Расстояние от низа в пикселях для триггера загрузки
    className?: string
}

export function InfiniteScroll({
    children,
    onLoadMore,
    hasMore,
    loading = false,
    threshold = 100,
    className,
}: InfiniteScrollProps) {
    const containerRef = useRef<HTMLDivElement>(null)
    const [isNearBottom, setIsNearBottom] = useState(false)

    useEffect(() => {
        const container = containerRef.current
        if (!container) return

        const handleScroll = () => {
            const { scrollTop, scrollHeight, clientHeight } = container
            const distanceFromBottom = scrollHeight - scrollTop - clientHeight
            
            setIsNearBottom(distanceFromBottom <= threshold)
        }

        container.addEventListener('scroll', handleScroll)
        return () => container.removeEventListener('scroll', handleScroll)
    }, [threshold])

    useEffect(() => {
        if (isNearBottom && hasMore && !loading) {
            onLoadMore()
        }
    }, [isNearBottom, hasMore, loading, onLoadMore])

    return (
        <div
            ref={containerRef}
            className={clsx(
                'overflow-auto',
                'scrollbar-thin scrollbar-track-transparent scrollbar-thumb-sidebar-accent/20 hover:scrollbar-thumb-sidebar-accent/40',
                'scroll-smooth',
                className
            )}
        >
            {children}
            {loading && (
                <div className="flex items-center justify-center py-4">
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-sidebar-accent border-t-transparent" />
                </div>
            )}
        </div>
    )
}
