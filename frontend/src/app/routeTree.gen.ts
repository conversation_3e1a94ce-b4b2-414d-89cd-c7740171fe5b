/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as PageLayoutRouteImport } from './routes/_page-layout'
import { Route as PageLayoutIndexRouteImport } from './routes/_page-layout/index'
import { Route as PageLayoutTestRouteImport } from './routes/_page-layout/test'

const PageLayoutRoute = PageLayoutRouteImport.update({
  id: '/_page-layout',
  getParentRoute: () => rootRouteImport,
} as any)
const PageLayoutIndexRoute = PageLayoutIndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => PageLayoutRoute,
} as any)
const PageLayoutTestRoute = PageLayoutTestRouteImport.update({
  id: '/test',
  path: '/test',
  getParentRoute: () => PageLayoutRoute,
} as any)

export interface FileRoutesByFullPath {
  '/test': typeof PageLayoutTestRoute
  '/': typeof PageLayoutIndexRoute
}
export interface FileRoutesByTo {
  '/test': typeof PageLayoutTestRoute
  '/': typeof PageLayoutIndexRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/_page-layout': typeof PageLayoutRouteWithChildren
  '/_page-layout/test': typeof PageLayoutTestRoute
  '/_page-layout/': typeof PageLayoutIndexRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths: '/test' | '/'
  fileRoutesByTo: FileRoutesByTo
  to: '/test' | '/'
  id: '__root__' | '/_page-layout' | '/_page-layout/test' | '/_page-layout/'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  PageLayoutRoute: typeof PageLayoutRouteWithChildren
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/_page-layout': {
      id: '/_page-layout'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof PageLayoutRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_page-layout/': {
      id: '/_page-layout/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof PageLayoutIndexRouteImport
      parentRoute: typeof PageLayoutRoute
    }
    '/_page-layout/test': {
      id: '/_page-layout/test'
      path: '/test'
      fullPath: '/test'
      preLoaderRoute: typeof PageLayoutTestRouteImport
      parentRoute: typeof PageLayoutRoute
    }
  }
}

interface PageLayoutRouteChildren {
  PageLayoutTestRoute: typeof PageLayoutTestRoute
  PageLayoutIndexRoute: typeof PageLayoutIndexRoute
}

const PageLayoutRouteChildren: PageLayoutRouteChildren = {
  PageLayoutTestRoute: PageLayoutTestRoute,
  PageLayoutIndexRoute: PageLayoutIndexRoute,
}

const PageLayoutRouteWithChildren = PageLayoutRoute._addFileChildren(
  PageLayoutRouteChildren,
)

const rootRouteChildren: RootRouteChildren = {
  PageLayoutRoute: PageLayoutRouteWithChildren,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
